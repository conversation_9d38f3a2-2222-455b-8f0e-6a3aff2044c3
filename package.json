{"name": "fmd-api", "version": "4.8.3", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "postbuild": "cp -r ./node_modules ./dist/ && npm run zip", "zip": "data_version=`node -p \"require('./package.json').version\"` && mkdir -p ./artifact && cd ./dist && zip -r ../artifact/fmd-api-$data_version.zip .", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node dist/main", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:e2e": "jest --config ./test/jest-e2e.json", "copy:ecosystem": "cp 'ecosystem.config.js' 'dist/'"}, "dependencies": {"@azure/cosmos": "^3.17.3", "@azure/identity": "^4.10.1", "@azure/keyvault-secrets": "^4.10.0", "@azure/storage-queue": "^12.26.0", "@nestjs/axios": "^4.0.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/cli": "^11.0.7", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@types/aes-js": "^3.1.4", "@types/pbkdf2": "^3.1.2", "aes-js": "^3.1.2", "applicationinsights": "^1.8.10", "atob": "^2.1.2", "axios": "^1.10.0", "cache-manager": "^6.3.2", "cookie-parser": "^1.4.7", "date-fns": "^2.29.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "pbkdf2": "^3.1.3", "redis": "^4.6.4", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rsa-pem-from-mod-exp": "^0.8.4"}, "devDependencies": {"@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.3", "@types/aes-js": "^3.1.4", "@types/atob": "^2.1.4", "@types/cache-manager": "^3.4.0", "@types/cookie-parser": "^1.4.9", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.10", "@types/lodash": "^4.17.19", "@types/node": "^14.18.10", "@types/pbkdf2": "^3.1.2", "@typescript-eslint/eslint-plugin": "^4.15.2", "@typescript-eslint/parser": "^4.15.2", "eslint": "^7.20.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.3.1", "jest": "^29.7.0", "mockdate": "^3.0.5", "prettier": "^2.2.1", "ts-jest": "^29.4.0", "ts-node": "^9.1.1", "typescript": "^4.1.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "roots": [".", "../test"], "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverage": true, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageReporters": ["json", "text-summary", "lcov", "clover"], "coverageDirectory": "../coverage", "testEnvironment": "node", "setupFilesAfterEnv": ["./jest.setup.ts"], "coveragePathIgnorePatterns": ["node_modules", "main.ts", "loadAppProperties.ts", "appinsights.ts", "app.*.ts", "../*.guard.ts", "../*.module.ts", "../*.controller.ts", "../*.model.ts", "../dto/", "utils/", "db/base.service.ts"], "reporters": ["default"], "coverageThreshold": {"global": {"statements": 93.2, "branches": 78.5, "functions": 94.5, "lines": 92.5}}}}